import { motion } from "framer-motion";
import { MessageCircle } from "lucide-react";
import { useNavigate } from "react-router-dom";
import type { Booking } from "@/types/booking";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";

interface BookingListItemProps {
  booking: Booking;
  onShareClick?: (booking: Booking) => void;
}

const BookingListItem = ({ booking, onShareClick }: BookingListItemProps) => {
  const navigate = useNavigate();

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed':
        return 'bg-primary/10 text-primary border-primary/20';
      case 'pending':
        return 'bg-amber-100 text-amber-800 border-amber-200';
      case 'cancelled':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'completed':
        return 'bg-emerald-100 text-emerald-800 border-emerald-200';
      default:
        return 'bg-muted text-muted-foreground border-border';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'confirmed':
        return 'Prossima';
      case 'pending':
        return 'In Attesa';
      case 'cancelled':
        return 'Cancellata';
      case 'completed':
        return 'Completata';
      default:
        return status;
    }
  };

  const formatBookingDate = (dateString: string) => {
    const date = new Date(dateString);
    const today = new Date();

    if (date.toDateString() === today.toDateString()) {
      return "Oggi";
    }

    return date.toLocaleDateString('it-IT', {
      weekday: 'long',
      day: 'numeric',
      month: 'short'
    });
  };

  const businessName = booking.deals?.businesses?.name || 'Attività';
  const dealTitle = booking.deals?.title || 'Servizio';
  const bookingDate = formatBookingDate(booking.booking_date);
  const bookingTime = booking.booking_time;
  const [hours, minutes] = bookingTime.split(':');
  const startTime = `${hours}:${minutes}`;

  const handleCancel = (e: React.MouseEvent) => {
    e.stopPropagation();
    // TODO: Implement cancel booking logic
    console.log('Cancel booking:', booking.id);
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-card rounded-2xl p-3 shadow-sm border border-border/50 hover:shadow-md transition-all duration-200"
    >
      <div className="flex items-start gap-3">
        {/* Business Image - Circular */}
        <div className="w-16 h-16 rounded-md overflow-hidden bg-muted shrink-0">
          <img
            className="w-full h-full object-cover"
            src={booking.deals?.images?.[0] || `https://picsum.photos/200/200?random=${booking.id}`}
            alt={businessName}
          />
        </div>

        {/* Main Content */}
        <div className="flex-1 min-w-0">
          {/* Header with business name and message icon */}
          <div className="flex items-start justify-between mb-1">
            <div className="flex-1 pr-2">
              <h3 className="font-semibold text-foreground text-base leading-tight">{businessName}</h3>
              <div className="flex items-center gap-2 mt-0.5">
                <span className="text-sm text-muted-foreground">{dealTitle}</span>
                
              </div>
              {/* Date and Time */}
              <div className="text-sm text-muted-foreground mt-1">
                {bookingDate} | {startTime}
              </div>
            </div>

           
          </div>
        </div>
      </div>

      {/* Action Buttons - Full width at bottom */}
      <div className="flex gap-2 mt-4">
        {booking.status === 'confirmed' &&  new Date(`${booking.booking_date}T${booking.booking_time}`) > new Date() &&  (
          <Button
            variant="outline"
            size="sm"
            className="flex-1 h-9 rounded-full border-primary/30 text-primary hover:bg-primary/5"
            onClick={handleCancel}
          >
            Cancella Prenotazione
          </Button>
        )}

      

        <Button
          variant="default"
          size="sm"
          className="flex-1 h-9 rounded-full bg-primary hover:bg-primary/90"
          onClick={() => navigate(`/prenotazione/${booking.id}`)}
        >
          {booking.status === 'pending' ? 'Riprogramma' : 'Dettagli'}
        </Button>
      </div>
    </motion.div>
  );
};

export default BookingListItem;